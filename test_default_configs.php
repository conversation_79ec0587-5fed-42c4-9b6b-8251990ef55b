<?php

require_once __DIR__ . '/bootstrap/app.php';

use App\DTOs\Xui\InboundDTO;
use App\Models\XuiServer;
use App\Services\Access\AccessService;

// Test the new default config selection logic
$accessService = new AccessService(app(\App\Services\HelperService::class));

// Create test server
$server = new XuiServer();
$server->address = 'gw2.smartvpn.vip';

// Test different inbound configurations
$testCases = [
    [
        'name' => 'VLESS + Reality',
        'streamSettings' => [
            'network' => 'tcp',
            'security' => 'reality',
            'realitySettings' => [
                'serverNames' => ['api.max.ru'],
                'shortIds' => ['b8a1bf2a297f3c0f'],
                'settings' => ['publicKey' => 'Ex0EgHtdE54vF1w3ZE2iTEA-7Pu1cl36nrETaRn4nm0']
            ]
        ]
    ],
    [
        'name' => 'VLESS + WebSocket + TLS',
        'streamSettings' => [
            'network' => 'ws',
            'security' => 'tls',
            'wsSettings' => ['path' => '/'],
            'tlsSettings' => ['serverName' => 'gw4.smartvpn.vip']
        ]
    ],
    [
        'name' => 'VLESS + XHTTP + TLS (gw2)',
        'streamSettings' => [
            'network' => 'xhttp',
            'security' => 'tls',
            'xhttpSettings' => ['path' => '/', 'host' => 'gw2.smartvpn.vip'],
            'tlsSettings' => ['serverName' => 'gw2.smartvpn.vip']
        ]
    ],
    [
        'name' => 'VLESS + XHTTP + TLS (gw4)',
        'streamSettings' => [
            'network' => 'xhttp',
            'security' => 'tls',
            'xhttpSettings' => ['path' => '/', 'host' => 'gw4.smartvpn.vip'],
            'tlsSettings' => ['serverName' => 'gw4.smartvpn.vip']
        ]
    ]
];

echo "Testing default config selection logic:\n\n";

foreach ($testCases as $testCase) {
    echo "Testing: {$testCase['name']}\n";
    
    // Create test inbound
    $inbound = new InboundDTO();
    $inbound->protocol = 'vless';
    $inbound->port = 443;
    $inbound->remark = 'Test ' . $testCase['name'];
    $inbound->streamSettings = $testCase['streamSettings'];
    
    // Test config path selection using reflection
    $reflection = new ReflectionClass($accessService);
    $method = $reflection->getMethod('getDefaultConfigPath');
    $method->setAccessible(true);
    
    try {
        $configPath = $method->invoke($accessService, $inbound, $server);
        $configFile = basename($configPath);
        
        if (file_exists($configPath)) {
            echo "  ✓ Selected config: {$configFile}\n";
            
            // Test if config is valid JSON
            $config = json_decode(file_get_contents($configPath), true);
            if (json_last_error() === JSON_ERROR_NONE) {
                echo "  ✓ Config is valid JSON\n";
                echo "  ✓ Outbounds count: " . count($config['outbounds'] ?? []) . "\n";
            } else {
                echo "  ✗ Config is invalid JSON: " . json_last_error_msg() . "\n";
            }
        } else {
            echo "  ✗ Config file not found: {$configPath}\n";
        }
    } catch (Exception $e) {
        echo "  ✗ Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

echo "Done!\n";
