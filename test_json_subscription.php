<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel application
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use App\Models\XuiServer;
use App\Services\Access\AccessService;
use App\DTOs\Xui\InboundDTO;

// Create a mock user
$user = new User();
$user->id = '0198938d-6ce8-7203-83b1-fdd90ad58233';
$user->email = '<EMAIL>';

// Create a mock server
$server = new XuiServer();
$server->address = 'gw4.smartvpn.vip';
$server->name = 'Test Server';
$server->server_load = 0.0; // Add server load

// Create a mock inbound with Reality settings similar to the example
$inboundData = [
    'id' => 1,
    'up' => 0,
    'down' => 0,
    'total' => 0,
    'remark' => '🇳🇱 Nederland - для WiFi и LTE',
    'enable' => true,
    'expiryTime' => 0,
    'port' => 443,
    'protocol' => 'vless',
    'settings' => json_encode([
        'clients' => [],
        'decryption' => 'none',
        'fallbacks' => []
    ]),
    'streamSettings' => json_encode([
        'network' => 'tcp',
        'security' => 'reality',
        'realitySettings' => [
            'show' => false,
            'dest' => 'gw4.smartvpn.vip:443',
            'serverNames' => ['gw4.smartvpn.vip'],
            'privateKey' => 'private_key_here',
            'shortIds' => ['3f'],
            'settings' => [
                'publicKey' => 'Ex0EgHtdE54vF1w3ZE2iTEA-7Pu1cl36nrETaRn4nm0',
                'fingerprint' => 'chrome',
                'mldsa65Verify' => ''
            ]
        ],
        'tcpSettings' => [
            'header' => [
                'type' => 'none'
            ]
        ],
        'externalProxy' => [
            [
                'forceTls' => 'same',
                'dest' => '**************',
                'port' => 443,
                'remark' => ''
            ]
        ]
    ]),
    'sniffing' => json_encode([
        'enabled' => true,
        'destOverride' => ['http', 'tls', 'quic', 'fakedns']
    ])
];

$inbound = new InboundDTO($inboundData);

// Create AccessService instance
$accessService = new AccessService(new \App\Services\HelperService());

// Generate JSON configuration
$configs = $accessService->generateJsonConfigForInbound($user, $server, $inbound);

// Output the result
echo json_encode($configs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
