<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Setting;
use App\Models\User;
use App\Services\Access\AccessService;
use App\Services\HelperService;
use Illuminate\Support\Facades\Log;

// Initialize Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Testing JSON Subscription Configuration\n";
echo "=====================================\n\n";

// Check current settings
echo "Current JSON subscription settings:\n";
echo "-----------------------------------\n";
echo "Format: " . Setting::getSubscriptionFormat() . "\n";
echo "Fragment: " . (Setting::getJsonSubscriptionFragment() ?: 'Not set') . "\n";
echo "Noises: " . (Setting::getJsonSubscriptionNoises() ?: 'Not set') . "\n";
echo "Mux: " . (Setting::getJsonSubscriptionMux() ?: 'Not set') . "\n\n";

// Set up default fragment and noises configurations if not set
$fragmentConfig = '{
    "tag": "fragment",
    "protocol": "freedom",
    "settings": {
        "domainStrategy": "AsIs",
        "fragment": {
            "packets": "tlshello",
            "length": "100-200",
            "interval": "10-20",
            "maxSplit": "300-400"
        }
    },
    "streamSettings": {
        "sockopt": {
            "tcpKeepAliveIdle": 100,
            "tcpMptcp": true,
            "penetrate": true
        }
    }
}';

$noisesConfig = '{
    "tag": "noises",
    "protocol": "freedom",
    "settings": {
        "domainStrategy": "AsIs",
        "noises": [
            {
                "type": "rand",
                "packet": "10-20",
                "delay": "10-16",
                "applyTo": "ip"
            }
        ]
    }
}';

// Set default configurations if not already set
if (empty(Setting::getJsonSubscriptionFragment())) {
    Setting::setJsonSubscriptionFragment($fragmentConfig);
    echo "✓ Set default fragment configuration\n";
}

if (empty(Setting::getJsonSubscriptionNoises())) {
    Setting::setJsonSubscriptionNoises($noisesConfig);
    echo "✓ Set default noises configuration\n";
}

// Load and validate default config
$defaultConfigPath = __DIR__ . '/app/Services/Access/default.json';
if (!file_exists($defaultConfigPath)) {
    echo "ERROR: Default config file not found!\n";
    exit(1);
}

$defaultConfig = json_decode(file_get_contents($defaultConfigPath), true);
if (json_last_error() !== JSON_ERROR_NONE) {
    echo "ERROR: Invalid JSON in default config: " . json_last_error_msg() . "\n";
    exit(1);
}

echo "✓ Default config loaded successfully.\n";
echo "Structure validation:\n";
echo "- DNS: " . (isset($defaultConfig['dns']) ? "✓" : "✗") . "\n";
echo "- Inbounds: " . (isset($defaultConfig['inbounds']) ? "✓" : "✗") . "\n";
echo "- Outbounds: " . (isset($defaultConfig['outbounds']) ? "✓" : "✗") . "\n";
echo "- Policy: " . (isset($defaultConfig['policy']) ? "✓" : "✗") . "\n";
echo "- Routing: " . (isset($defaultConfig['routing']) ? "✓" : "✗") . "\n";
echo "- Stats: " . (isset($defaultConfig['stats']) ? "✓" : "✗") . "\n\n";

// Test JSON generation with a test user
echo "Testing JSON generation with test user:\n";
echo "--------------------------------------\n";

try {
    // Find a test user or create one
    $testUser = User::where('email', '<EMAIL>')->first();
    if (!$testUser) {
        echo "No test user found. Please create a test user first.\n";
        exit(1);
    }

    echo "✓ Found test user: {$testUser->email} (ID: {$testUser->id})\n";

    // Initialize AccessService
    $helperService = new HelperService();
    $accessService = new AccessService($helperService);

    // Generate JSON subscription
    echo "Generating JSON subscription...\n";
    $jsonSubscription = $accessService->generateJsonSubscription($testUser);

    if (empty($jsonSubscription) || $jsonSubscription === '[]') {
        echo "⚠ No JSON subscription generated (empty result)\n";
        echo "This might be because:\n";
        echo "- User has no active subscription\n";
        echo "- User has no active server pools\n";
        echo "- No active servers in pools\n";
        echo "- No enabled inbounds on servers\n\n";
    } else {
        echo "✓ JSON subscription generated successfully\n";

        // Parse and validate generated JSON
        $generatedConfig = json_decode($jsonSubscription, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "✗ ERROR: Generated JSON is invalid: " . json_last_error_msg() . "\n";
            echo "Generated content:\n" . $jsonSubscription . "\n";
        } else {
            echo "✓ Generated JSON is valid\n";
            echo "Number of configurations: " . count($generatedConfig) . "\n\n";

            if (!empty($generatedConfig)) {
                $firstConfig = $generatedConfig[0];
                echo "First generated config structure:\n";
                echo "- DNS: " . (isset($firstConfig['dns']) ? "✓" : "✗") . "\n";
                echo "- Inbounds: " . (isset($firstConfig['inbounds']) ? "✓" : "✗") . "\n";
                echo "- Outbounds: " . (isset($firstConfig['outbounds']) ? "✓" : "✗") . "\n";
                echo "- Policy: " . (isset($firstConfig['policy']) ? "✓" : "✗") . "\n";
                echo "- Routing: " . (isset($firstConfig['routing']) ? "✓" : "✗") . "\n";
                echo "- Stats: " . (isset($firstConfig['stats']) ? "✓" : "✗") . "\n";
                echo "- Remarks: " . (isset($firstConfig['remarks']) ? "✓" : "✗") . "\n\n";

                // Check outbounds structure
                if (isset($firstConfig['outbounds'])) {
                    echo "Outbounds in generated config:\n";
                    foreach ($firstConfig['outbounds'] as $i => $outbound) {
                        $tag = $outbound['tag'] ?? 'unknown';
                        $protocol = $outbound['protocol'] ?? 'unknown';
                        echo "  {$i}: {$tag} ({$protocol})\n";
                    }
                    echo "\n";
                }

                // Validate against example structure
                echo "Comparing with example structure:\n";
                $exampleConfigPath = __DIR__ . '/app/Services/Access/JSON_SUBSCRIPTION_EXAMPLE.md';
                if (file_exists($exampleConfigPath)) {
                    $exampleContent = file_get_contents($exampleConfigPath);

                    // Extract JSON from markdown
                    if (preg_match('/```json\s*(.*?)\s*```/s', $exampleContent, $matches)) {
                        $exampleConfig = json_decode($matches[1], true);

                        if (json_last_error() === JSON_ERROR_NONE && is_array($exampleConfig) && !empty($exampleConfig[0])) {
                            $exampleFirst = $exampleConfig[0];

                            // Compare structures
                            $structureMatches = true;
                            $requiredKeys = ['dns', 'inbounds', 'outbounds', 'policy', 'routing', 'stats', 'remarks'];

                            foreach ($requiredKeys as $key) {
                                if (isset($exampleFirst[$key]) !== isset($firstConfig[$key])) {
                                    echo "✗ Structure mismatch: {$key}\n";
                                    $structureMatches = false;
                                } else {
                                    echo "✓ Structure match: {$key}\n";
                                }
                            }

                            if ($structureMatches) {
                                echo "✓ Generated config structure matches example\n";
                            } else {
                                echo "✗ Generated config structure differs from example\n";
                            }
                        }
                    }
                }

                // Show sample of generated JSON (first 1000 chars)
                echo "\nSample of generated JSON:\n";
                echo "------------------------\n";
                echo substr($jsonSubscription, 0, 1000) . "...\n\n";
            }
        }
    }

} catch (\Exception $e) {
    echo "✗ ERROR during JSON generation: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "JSON structure test completed.\n";
