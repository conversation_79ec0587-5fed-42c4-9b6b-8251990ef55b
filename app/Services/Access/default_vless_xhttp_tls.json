{"log": {"loglevel": "warning"}, "dns": {"servers": ["*******", "*******", "*******", "*******", "*******"]}, "inbounds": [{"listen": "127.0.0.1", "port": 10808, "protocol": "mixed", "settings": {"auth": "<PERSON><PERSON><PERSON>", "udp": true, "userLevel": 8}, "sniffing": {"enabled": true, "destOverride": ["http", "tls", "quic"]}}], "outbounds": [{"tag": "proxy", "protocol": "vless", "mux": {"enabled": true, "concurrency": 4}, "settings": {"vnext": [{"address": "gw2.smartvpn.vip", "port": 443, "users": [{"id": "0198938d-6ce8-7203-83b1-fdd90ad58233", "encryption": "none"}]}]}, "streamSettings": {"network": "xhttp", "security": "tls", "tlsSettings": {"serverName": "gw2.smartvpn.vip", "fingerprint": "chrome"}, "xhttpSettings": {"host": "gw2.smartvpn.vip", "path": "/", "mode": "packet-up"}}}, {"tag": "direct", "protocol": "freedom"}, {"tag": "block", "protocol": "blackhole"}], "routing": {"rules": [{"type": "field", "network": "tcp,udp", "outboundTag": "proxy"}]}}