{"log": {"loglevel": "warning"}, "dns": {"queryStrategy": "UseIP", "servers": ["*******", "*******", "*******", "*******", "*******"]}, "inbounds": [{"tag": "mixed-in", "listen": "127.0.0.1", "port": 10808, "protocol": "mixed", "settings": {"auth": "<PERSON><PERSON><PERSON>", "udp": true, "userLevel": 8}, "sniffing": {"enabled": true, "destOverride": ["http", "tls", "quic"]}}], "outbounds": [{"tag": "proxy", "protocol": "vless", "settings": {"vnext": [{"address": "**************", "port": 443, "users": [{"id": "0198938d-6ce8-7203-83b1-fdd90ad58233", "encryption": "none", "flow": "xtls-rprx-vision"}]}]}, "streamSettings": {"network": "tcp", "security": "reality", "realitySettings": {"fingerprint": "chrome", "serverName": "api.max.ru", "publicKey": "Ex0EgHtdE54vF1w3ZE2iTEA-7Pu1cl36nrETaRn4nm0", "shortId": "b8a1bf2a297f3c0f"}, "sockopt": {"dialerProxy": "fragment"}}}, {"tag": "fragment", "protocol": "freedom", "settings": {"domainStrategy": "AsIs", "fragment": {"packets": "tlshello", "length": "100-200", "interval": "10-20"}}}, {"tag": "direct", "protocol": "freedom"}, {"tag": "block", "protocol": "blackhole"}], "routing": {"domainStrategy": "AsIs", "rules": [{"type": "field", "network": "tcp,udp", "outboundTag": "proxy"}]}, "policy": {"levels": {"8": {"handshake": 4, "connIdle": 300, "uplinkOnly": 1, "downlinkOnly": 1}}}}