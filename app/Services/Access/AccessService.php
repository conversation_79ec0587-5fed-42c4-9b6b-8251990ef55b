<?php

namespace App\Services\Access;

use App\DTOs\Xui\InboundDTO;
use App\Models\User;
use App\Models\Announce;
use App\Models\Setting;
use App\Models\SniEndpoint;
use App\Services\HelperService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use App\Models\XuiServer;
use App\Services\ServerInfoService;
use App\Services\Traffic\TrafficLoggerService;
use stdClass;

class AccessService
{
    public function __construct(
        private HelperService $helperService
    ) {}

    /**
     * Generate subscription content for a user based on format setting.
     * Returns either VLESS links (text) or JSON configuration.
     */
    public function generateSubscriptionContent(User $user): string
    {
        $format = Setting::getSubscriptionFormat();

        if ($format === 'json') {
            return $this->generateJsonSubscription($user);
        }

        return $this->generateVlessKeys($user);
    }

    /**
     * Generate VLESS keys for a user based on their active subscription and available servers.
     *
     * @var XuiServer $server
     * @var InboundDTO $inbound
     */
    public function generateVlessKeys(User $user): string
    {
        // Get user's active subscription
        $subscription = $user->currentSubscription;
        if (!$subscription || !$subscription->isActive()) {
            Log::info("No active subscription found for user {$user->id}");
            return '';
        }

        // Get server pool from user's active server pools
        $serverPools = $user->serverPools()
                ->where('server_pools.is_active', true)
                ->whereNull('user_server_assignments.released_at')
                ->with('servers')
                ->get();

        if ($serverPools->isEmpty()) {
            Log::warning("No active server pools found for user {$user->id}");
            return '';
        }

        // Use the first active server pool
        /** @var ServerPool $serverPool */
        $serverPool = $serverPools->first();

        // Get active servers in the pool
        $servers = $serverPool->activeServers()->get();
        if ($servers->isEmpty()) {
            Log::warning("No active servers found in pool {$serverPool->id}");
            return '';
        }

        $vlessLinks = [];

        foreach ($servers as $server) {
            /** @var XuiServer $server */
            // Get inbounds for this server
            $inbounds = $server->inbounds();

            foreach ($inbounds as $inbound) {
                /** @var InboundDTO $inbound */

                // skip if remark contains test word or starts with dot
                if (str_contains($inbound->remark, 'test') || str_starts_with($inbound->remark, '.')) {
                    // continue;
                }

                // skip if inbound is not enabled
                if (!$inbound->enable) {
                    continue;
                }

                try {
                    $vlessUrl = $this->generateVlessUrl($user, $server, $inbound);
                    if ($vlessUrl) {
                        $vlessLinks[] = $vlessUrl;
                    }
                } catch (\Exception $e) {
                    Log::error("Error generating VLESS URL for user {$user->id}", [
                        'server' => $server->name,
                        'inbound' => $inbound->remark,
                        'error' => $e->getMessage(),
                    ]);
                }
            }
        }

        if (empty($vlessLinks)) {
            Log::warning("No VLESS links generated for user {$user->id}");
            return '';
        }

        return implode("\n", $vlessLinks);
    }

    /**
     * Generate JSON subscription for a user based on their active subscription and available servers.
     */
    public function generateJsonSubscription(User $user): string
    {
        // Get user's active subscription
        $subscription = $user->currentSubscription;
        if (!$subscription || !$subscription->isActive()) {
            Log::info("No active subscription found for user {$user->id}");
            return '[]';
        }

        // Get server pool from user's active server pools
        $serverPools = $user->serverPools()
                ->where('server_pools.is_active', true)
                ->whereNull('user_server_assignments.released_at')
                ->with('servers')
                ->get();

        if ($serverPools->isEmpty()) {
            Log::warning("No active server pools found for user {$user->id}");
            return '[]';
        }

        // Use the first active server pool
        $serverPool = $serverPools->first();

        // Get active servers in the pool
        $servers = $serverPool->activeServers()->get();
        if ($servers->isEmpty()) {
            Log::warning("No active servers found in pool {$serverPool->id}");
            return '[]';
        }

        $configArray = [];

        foreach ($servers as $server) {
            /** @var XuiServer $server */
            // Get inbounds for this server
            $inbounds = $server->inbounds();

            foreach ($inbounds as $inbound) {
                /** @var InboundDTO $inbound */

                // skip if remark contains test word or starts with dot
                if (str_contains(mb_strtolower($inbound->remark), 'test') || str_starts_with($inbound->remark, '.')) {
                    continue;
                }

                // skip if inbound is not enabled
                if (!$inbound->enable) {
                    continue;
                }

                try {
                    $configs = $this->generateJsonConfigForInbound($user, $server, $inbound);
                    $configArray = array_merge($configArray, $configs);
                } catch (\Exception $e) {
                    Log::error("Error generating JSON config for user {$user->id}", [
                        'server' => $server->name,
                        'inbound' => $inbound->remark,
                        'error' => $e->getMessage(),
                    ]);
                }
            }
        }

        if (empty($configArray)) {
            Log::warning("No JSON configs generated for user {$user->id}");
            return '[]';
        }

        // Always return array of configs like in 3XUI
        return json_encode($configArray, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * Generate JSON configuration for a specific inbound.
     */
    public function generateJsonConfigForInbound(User $user, XuiServer $server, InboundDTO $inbound): array
    {
        $streamSettings = $inbound->streamSettings;
        $externalProxies = $streamSettings['externalProxy'] ?? [];

        // If no external proxies, create default one
        if (empty($externalProxies)) {
            $externalProxies = [[
                'forceTls' => 'same',
                'dest' => $server->address,
                'port' => $inbound->port,
                'remark' => '',
            ]];
        }

        $configs = [];

        foreach ($externalProxies as $externalProxy) {
            $config = $this->buildJsonConfig($user, $server, $inbound, $externalProxy);
            $configs[] = $config;
        }

        return $configs;
    }

    /**
     * Build a single JSON configuration.
     */
    private function buildJsonConfig(User $user, XuiServer $server, InboundDTO $inbound, array $externalProxy): array
    {
        // Load default configuration
        $defaultConfigPath = __DIR__ . '/default.json';
        $defaultConfig = json_decode(file_get_contents($defaultConfigPath), true);

        // Create deep copy of default configuration to avoid modifying original
        $config = json_decode(json_encode($defaultConfig), true);

        // Ensure stats is an object (must be `{}` for Xray; PHP would encode empty array as `[]`)
        $config['stats'] = new stdClass();

        // Generate outbound for this inbound
        $outbound = $this->generateOutboundForInbound($user, $server, $inbound, $externalProxy);

        // Add default outbounds (fragment, noises, etc.)
        $defaultOutbounds = $this->getDefaultOutbounds();

        // Combine outbounds: proxy first, then defaults
        $config['outbounds'] = array_merge([$outbound], $defaultOutbounds);

        // Set remarks (note: plural form like in 3XUI)
        $serverLoad = $server->server_load ?? 0.0;
        $customString = ' ' . app(ServerInfoService::class)->toBrailleLoadChar($serverLoad);
        $remark = $inbound->remark . $customString . '-' . $user->getPublicClientId();
        $config['remarks'] = $remark;

        return $config;
    }

    /**
     * Generate outbound configuration for a specific inbound.
     */
    public function generateOutboundForInbound(User $user, XuiServer $server, InboundDTO $inbound, array $externalProxy): array
    {
        $streamSettings = $inbound->streamSettings;
        $address = $externalProxy['dest'] ?? $server->address;
        $port = $externalProxy['port'] ?? $inbound->port;
        $forceTls = $externalProxy['forceTls'] ?? 'same';

        // Process stream settings
        $processedStream = $this->processStreamSettings($streamSettings, $forceTls);

        $outbound = [
            'protocol' => $inbound->protocol,
            'tag' => 'proxy',
            'streamSettings' => $processedStream,
        ];

        // Add mux if configured, otherwise use default
        $mux = Setting::getJsonSubscriptionMux();
        if (!empty($mux)) {
            $outbound['mux'] = json_decode($mux, true);
        } else {
            // Default mux configuration like in 3XUI
            $outbound['mux'] = [
                'enabled' => true,
                'concurrency' => 4,
                'xudpConcurrency' => 8,
                'xudpProxyUDP443' => 'allow'
            ];
        }

        // Generate settings based on protocol
        switch ($inbound->protocol) {
            case 'vless':
                $outbound['settings'] = $this->generateVlessSettings($user, $address, $port, $inbound);
                break;
            case 'vmess':
                $outbound['settings'] = $this->generateVmessSettings($user, $address, $port, $inbound);
                break;
            case 'trojan':
            case 'shadowsocks':
                $outbound['settings'] = $this->generateServerSettings($user, $address, $port, $inbound);
                break;
        }

        return $outbound;
    }

    /**
     * Get default outbounds (direct, block, fragment, noises).
     */
    private function getDefaultOutbounds(): array
    {
        $outbounds = [
            [
                'protocol' => 'freedom',
                'settings' => [
                    'domainStrategy' => 'AsIs',
                    'noises' => [],
                    'redirect' => ''
                ],
                'tag' => 'direct'
            ],
            [
                'protocol' => 'blackhole',
                'settings' => [
                    'response' => [
                        'type' => 'http'
                    ]
                ],
                'tag' => 'block'
            ]
        ];

        // Add fragment if configured
        $fragment = Setting::getJsonSubscriptionFragment();
        if (!empty($fragment)) {
            $outbounds[] = json_decode($fragment, true);
        }

        // Add noises if configured
        $noises = Setting::getJsonSubscriptionNoises();
        if (!empty($noises)) {
            $outbounds[] = json_decode($noises, true);
        }

        return $outbounds;
    }

    /**
     * Process stream settings for JSON configuration.
     */
    private function processStreamSettings(array $streamSettings, string $forceTls): array
    {
        $processed = [];

        // Remove externalProxy from streamSettings (it's handled separately)
        unset($streamSettings['externalProxy']);

        // Copy basic network settings
        $processed['network'] = $streamSettings['network'] ?? 'tcp';

        // Handle security settings
        $security = $forceTls === 'same' ? ($streamSettings['security'] ?? 'none') : $forceTls;
        $processed['security'] = $security;

        // Process security-specific settings
        switch ($security) {
            case 'tls':
                if (isset($streamSettings['tlsSettings'])) {
                    $processed['tlsSettings'] = $this->processTlsSettings($streamSettings['tlsSettings']);
                }
                break;
            case 'reality':
                if (isset($streamSettings['realitySettings'])) {
                    $processed['realitySettings'] = $this->processRealitySettings($streamSettings['realitySettings']);
                }
                break;
            case 'none':
                // No additional settings needed for none
                break;
        }

        // Add sockopt with fragment if configured
        $fragment = Setting::getJsonSubscriptionFragment();
        if (!empty($fragment)) {
            $processed['sockopt'] = [
                'dialerProxy' => 'fragment',
                'tcpKeepAliveIdle' => 100,
                'tcpMptcp' => true,
                'penetrate' => true
            ];
        }

        // Copy network-specific settings without proxy protocol
        $network = $processed['network'];
        switch ($network) {
            case 'tcp':
                if (isset($streamSettings['tcpSettings'])) {
                    $tcpSettings = $streamSettings['tcpSettings'];
                    unset($tcpSettings['acceptProxyProtocol']);
                    $processed['tcpSettings'] = $tcpSettings;
                }
                break;
            case 'ws':
                if (isset($streamSettings['wsSettings'])) {
                    $wsSettings = $streamSettings['wsSettings'];
                    unset($wsSettings['acceptProxyProtocol']);
                    $processed['wsSettings'] = $wsSettings;
                }
                break;
            case 'httpupgrade':
                if (isset($streamSettings['httpupgradeSettings'])) {
                    $httpSettings = $streamSettings['httpupgradeSettings'];
                    unset($httpSettings['acceptProxyProtocol']);
                    $processed['httpupgradeSettings'] = $httpSettings;
                }
                break;
            case 'grpc':
                if (isset($streamSettings['grpcSettings'])) {
                    $processed['grpcSettings'] = $streamSettings['grpcSettings'];
                }
                break;
            case 'xhttp':
                if (isset($streamSettings['xhttpSettings'])) {
                    $xhttpSettings = $streamSettings['xhttpSettings'];
                    unset($xhttpSettings['acceptProxyProtocol']);
                    $processed['xhttpSettings'] = $xhttpSettings;
                }
                break;
        }

        return $processed;
    }

    /**
     * Process TLS settings for JSON configuration.
     */
    private function processTlsSettings(array $tlsSettings): array
    {
        $processed = [];
        $settings = $tlsSettings['settings'] ?? [];

        if (isset($tlsSettings['serverName'])) {
            $processed['serverName'] = $tlsSettings['serverName'];
        }

        if (isset($tlsSettings['alpn'])) {
            $processed['alpn'] = $tlsSettings['alpn'];
        }

        if (isset($settings['fingerprint'])) {
            $processed['fingerprint'] = $settings['fingerprint'];
        }

        return $processed;
    }

    /**
     * Process Reality settings for JSON configuration.
     */
    public function processRealitySettings(array $realitySettings): array
    {
        $processed = [];
        $settings = $realitySettings['settings'] ?? [];

        $processed['show'] = false;

        if (isset($settings['publicKey'])) {
            $processed['publicKey'] = $settings['publicKey'];
        }

        if (isset($settings['fingerprint'])) {
            $processed['fingerprint'] = $settings['fingerprint'];
        }

        if (isset($settings['mldsa65Verify'])) {
            $processed['mldsa65Verify'] = $settings['mldsa65Verify'];
        }

        // Generate random spiderX
        $processed['spiderX'] = '/' . \Illuminate\Support\Str::random(15);

        // Handle shortIds - use first one if available
        if (isset($realitySettings['shortIds']) && is_array($realitySettings['shortIds']) && !empty($realitySettings['shortIds'])) {
            $processed['shortId'] = $realitySettings['shortIds'][0];
        } else {
            $processed['shortId'] = '';
        }

        // Handle serverNames - use first one if available
        if (isset($realitySettings['serverNames']) && is_array($realitySettings['serverNames']) && !empty($realitySettings['serverNames'])) {
            $processed['serverName'] = $realitySettings['serverNames'][0];
        } else {
            $processed['serverName'] = '';
        }

        return $processed;
    }

    /**
     * Generate VLESS settings for outbound.
     */
    public function generateVlessSettings(User $user, string $address, int $port, InboundDTO $inbound): array
    {
        $settings = [
            'address' => $address,
            'port' => $port,
            'id' => $user->id, // Use user UUID directly
            'encryption' => 'none', // Default for VLESS
        ];

        // Add flow if needed for Reality/TLS with TCP
        $streamSettings = $inbound->streamSettings;
        if (($streamSettings['security'] === 'reality' || $streamSettings['security'] === 'tls')
            && $streamSettings['network'] === 'tcp') {
            $settings['flow'] = 'xtls-rprx-vision';
        }

        // Override encryption from inbound settings if present
        $inboundSettings = $inbound->settings;
        if (isset($inboundSettings['encryption'])) {
            $settings['encryption'] = $inboundSettings['encryption'];
        }

        return $settings;
    }

    /**
     * Generate VMess settings for outbound.
     */
    private function generateVmessSettings(User $user, string $address, int $port, InboundDTO $inbound): array
    {
        $users = [[
            'id' => $user->id, // Use user UUID directly
            'email' => $user->email ?? '',
            'security' => 'auto', // Default security for VMess
        ]];

        return [
            'vnext' => [[
                'address' => $address,
                'port' => $port,
                'users' => $users,
            ]]
        ];
    }

    /**
     * Generate server settings for Trojan/Shadowsocks outbound.
     */
    private function generateServerSettings(User $user, string $address, int $port, InboundDTO $inbound): array
    {
        $server = [
            'address' => $address,
            'port' => $port,
            'level' => 8,
            'password' => $user->id, // Use user UUID as password
        ];

        // For Shadowsocks, add method
        if ($inbound->protocol === 'shadowsocks') {
            $inboundSettings = $inbound->settings;
            if (isset($inboundSettings['method'])) {
                $server['method'] = $inboundSettings['method'];

                // Handle 2022 protocols with server password
                if (str_starts_with($inboundSettings['method'], '2022') && isset($inboundSettings['password'])) {
                    $server['password'] = $inboundSettings['password'] . ':' . $user->id;
                }
            }
        }

        return [
            'servers' => [$server]
        ];
    }

    /**
     * Generate VLESS URL for a specific inbound using the new genVLESSLink method
     */
    public function generateVlessUrlForInbound(User $user, XuiServer $server, InboundDTO $inbound): string
    {
        $streamSettings = $inbound->streamSettings;
        $userUuid = $user->id;



        // Determine security setting
        $forceTls = 'same'; // Use the same security as configured in stream settings

        // Determine flow setting
        $flow = null;
        if ($streamSettings['security'] === 'reality' || $streamSettings['security'] === 'tls') {
            if ($streamSettings['network'] === 'tcp') {
                $flow = 'xtls-rprx-vision-udp443'; // Default flow for reality/tls with tcp
            }
        }

        // Build remark with server load info
        $serverLoad = $server->server_load ?? 0.0;
        $customString = ' ' . app(ServerInfoService::class)->toBrailleLoadChar($serverLoad);
        $remark = $inbound->remark . $customString;
        $remark = str_replace(' ', ' ', $remark);

        $externalProxy = $streamSettings['externalProxy'] ?? [];

        // Generate VLESS link using the new method
        return $this->genVLESSLink(
            stream: $streamSettings,
            address: $externalProxy[0]['dest'] ?? $server->address,
            port: $externalProxy[0]['port'] ?? $inbound->port,
            forceTls: $forceTls,
            remark: $remark,
            clientId: $userUuid,
            flow: $flow
        );
    }

    /**
     * Generate VLESS link from stream settings (converted from JavaScript)
     *
     * @param array $stream Stream settings array
     * @param string $address Server address
     * @param int|null $port Server port
     * @param string $forceTls Force TLS setting ('same', 'tls', 'reality', 'none')
     * @param string $remark Connection remark/name
     * @param string $clientId Client UUID
     * @param string|null $flow Flow setting for XTLS
     * @return string Generated VLESS URL
     */
    private function genVLESSLink(array $stream, string $address = '', ?int $port = null, string $forceTls = 'same', string $remark = '', string $clientId = '', ?string $flow = null): string
    {
        $uuid = $clientId;
        $type = $stream['network'] ?? '';
        $security = $forceTls === 'same' ? ($stream['security'] ?? '') : $forceTls;
        $params = [];

        // Set basic parameters
        $params['type'] = $type;
        $params['encryption'] = $stream['settings']['encryption'] ?? 'none';

        // Handle different network types
        switch ($type) {
            case 'tcp':
                $tcp = $stream['tcpSettings'] ?? [];
                if (($tcp['header']['type'] ?? '') === 'http') {
                    $request = $tcp['header']['request'] ?? [];
                    if (isset($request['path']) && is_array($request['path'])) {
                        $params['path'] = implode(',', $request['path']);
                    }

                    // Find host header
                    $headers = $request['headers'] ?? [];
                    foreach ($headers as $header) {
                        if (isset($header['name']) && strtolower($header['name']) === 'host') {
                            $params['host'] = $header['value'] ?? '';
                            break;
                        }
                    }
                    $params['headerType'] = 'http';
                }
                break;

            case 'kcp':
                // KCP settings can be added here if needed
                break;

            case 'ws':
                $ws = $stream['wsSettings'] ?? [];
                $params['path'] = $ws['path'] ?? '';
                $params['host'] = !empty($ws['host']) ? $ws['host'] : $this->getHeaderFromSettings($ws, 'host');
                break;

            case 'grpc':
                $grpc = $stream['grpcSettings'] ?? [];
                $params['serviceName'] = $grpc['serviceName'] ?? '';
                $params['authority'] = $grpc['authority'] ?? '';
                if (!empty($grpc['multiMode'])) {
                    $params['mode'] = 'multi';
                }
                break;

            case 'httpupgrade':
                $httpupgrade = $stream['httpupgradeSettings'] ?? [];
                $params['path'] = $httpupgrade['path'] ?? '';
                $params['host'] = !empty($httpupgrade['host']) ? $httpupgrade['host'] : $this->getHeaderFromSettings($httpupgrade, 'host');
                break;

            case 'xhttp':
                $xhttp = $stream['xhttpSettings'] ?? [];
                $params['path'] = $xhttp['path'] ?? '';
                $params['host'] = !empty($xhttp['host']) ? $xhttp['host'] : $this->getHeaderFromSettings($xhttp, 'host');
                $params['mode'] = $xhttp['mode'] ?? '';
                break;
        }

        // Handle security settings
        if ($security === 'tls') {
            $params['security'] = 'tls';
            $tls = $stream['tlsSettings'] ?? [];
            $tlsSettings = $tls['settings'] ?? [];

            if (isset($tlsSettings['fingerprint'])) {
                $params['fp'] = $tlsSettings['fingerprint'];
            }

            if (isset($tls['alpn']) && is_array($tls['alpn'])) {
                $params['alpn'] = implode(',', $tls['alpn']);
            }

            if (!empty($tls['serverName'])) {
                $params['sni'] = $tls['serverName'];
            }

            if (!empty($tlsSettings['echConfigList'])) {
                $params['ech'] = $tlsSettings['echConfigList'];
            }

            if ($type === 'tcp' && !empty($flow)) {
                $params['flow'] = $flow;
            }
        } elseif ($security === 'reality') {
            $params['security'] = 'reality';
            $reality = $stream['realitySettings'] ?? [];
            $realitySettings = $reality['settings'] ?? [];

            if (isset($realitySettings['publicKey'])) {
                $params['pbk'] = $realitySettings['publicKey'];
            }

            if (isset($realitySettings['fingerprint'])) {
                $params['fp'] = $realitySettings['fingerprint'];
            }

            if (!empty($reality['serverNames'])) {
                $serverNames = is_string($reality['serverNames']) ?
                    explode(',', $reality['serverNames']) :
                    $reality['serverNames'];
                if (!empty($serverNames)) {
                    $params['sni'] = trim($serverNames[0]);
                }
            }

            if (!empty($reality['shortIds'])) {
                $shortIds = is_string($reality['shortIds']) ?
                    explode(',', $reality['shortIds']) :
                    $reality['shortIds'];
                if (!empty($shortIds)) {
                    $params['sid'] = trim($shortIds[0]);
                }
            }

            if (!empty($realitySettings['spiderX'])) {
                $params['spx'] = $realitySettings['spiderX'];
            }

            if (!empty($realitySettings['mldsa65Verify'])) {
                $params['pqv'] = $realitySettings['mldsa65Verify'];
            }

            if ($type === 'tcp' && !empty($flow)) {
                $params['flow'] = $flow;
            }
        } else {
            $params['security'] = 'none';
        }

        // Build the VLESS URL
        $link = "vless://{$uuid}@{$address}:{$port}";

        // Add query parameters
        $queryString = http_build_query(array_filter($params, function($value) {
            return $value !== '' && $value !== null;
        }));

        if (!empty($queryString)) {
            $link .= '?' . $queryString;
        }

        // Add remark as fragment
        if (!empty($remark)) {
            $link .= '#' . urlencode($remark);
        }

        return $link;
    }

    /**
     * Helper method to extract header value from settings (equivalent to JS getHeader method)
     *
     * @param array $settings Settings array
     * @param string $headerName Header name to find
     * @return string Header value or empty string
     */
    private function getHeaderFromSettings(array $settings, string $headerName): string
    {
        $headers = $settings['headers'] ?? [];

        foreach ($headers as $header) {
            if (isset($header['name']) && strtolower($header['name']) === strtolower($headerName)) {
                return $header['value'] ?? '';
            }
        }

        return '';
    }

    /**
     * Generate a single VLESS URL for user, server, and inbound.
     */
    private function generateVlessUrl(User $user, XuiServer $server, InboundDTO $inbound): string
    {
        // Use the new generateVlessUrlForInbound method which uses genVLESSLink
        return $this->generateVlessUrlForInbound($user, $server, $inbound);
    }

    /**
     * Get routing settings for user.
     */
    public function getRoutingSettings(User $user): string
    {
        $routingRules = Setting::get('common_routing_rules', '', true);

        // If user has disabled common routing, return empty string
        if (! $user->use_common_routing) {
            return '';
        }

        // If no routing rules, return empty string
        if (empty($routingRules)) {
            return '';
        }

        // If routing rules is array, encode it to JSON
        if (is_array($routingRules)) {
            $routingRules = json_encode($routingRules);
        }

        // Encode routing rules to base64
        $routingRules = base64_encode(HelperService::encodeJsonUtf8Safe($routingRules) ?? $routingRules);

        return $routingRules;
    }

    /**
     * Get subscription headers for VPN clients.
     */
    public function getSubscriptionHeaders(User $user, string $uuid): array
    {
        $announceData = $this->getAnnounceData($user, $uuid);

        // Determine routing rules based on user preference
        $routingRules = $this->getRoutingSettings($user);

        $headers = [
            'Profile-Title' => 'base64:' . base64_encode($this->getSubscriptionTitle($user)),
            'Content-Type' => 'text/plain; charset=utf-8',
            'Profile-Update-Interval' => '1', // update interval in hours
            'Profile-Web-Page-Url' => \App\Services\HelperService::getCurrentAppUrl() . '/access/' . $uuid,
            'Support-Url' => \App\Services\HelperService::getCurrentAppUrl() . '/support/' . $uuid,
            'Announce' => $announceData['text'] ? 'base64:' . base64_encode($announceData['text']) : '',
            'Announce-Url' => $announceData['url'] ?: '',
            'Update-always' => 'true',
            'hide-settings' => 'true',
            'subscription-always-hwid-enable' => 'true',
            'subscription-auto-update-enable' => 'true',
            'subscription-auto-update-open-enable' => 'true',
            // больше не используется роутинг, нет смысла при блокировках
            // 'Routing' => $routingRules,
        ];

        // Add subscription userinfo
        $userinfo = $this->buildSubscriptionUserinfo($user);
        if ($userinfo) {
            $headers['Subscription-Userinfo'] = $userinfo;
        }

        return $headers;
    }

    /**
     * Get subscription title.
     */
    private function getSubscriptionTitle(User $user): string
    {
        $title = config("app.access.title", "SmartVPN");

        $title .= " \n       Ваш ID: {$user->public_client_id} 🔰";

        return $title;
    }

    /**
     * Get announce data based on subscription status.
     */
    private function getAnnounceData(User $user, string $uuid): array
    {
        $defaultText = config("app.access_announce", "");
        $defaultUrl = config("app.access_announce_url", "");

        try {
            $defaultUrl = HelperService::replaceTextVariables($defaultUrl, $user);

            // Get current subscription
            $currentSubscription = $user->currentSubscription;
            $earliestExpiry = $currentSubscription?->end_date;

            if ($earliestExpiry) {
                $now = Carbon::now();
                $expiryTime = Carbon::parse($earliestExpiry);

                // Check if subscription has already expired
                if ($expiryTime->isPast()) {
                    return [
                        'text' => "❗️ #c11e14ПОДПИСКА #c11e14ИСТЕКЛА ❗️\n\nНажмите сюда, чтобы продлить подписку 💳",
                        'url' => HelperService::getCurrentAppUrl() . "/access/{$uuid}/plan/select",
                    ];
                }

                // Check if expiry is within 3 days
                if ($expiryTime->isFuture() && $now->diffInDays($expiryTime, false) < 3) {
                    $remainingTime = HelperService::formatRemainingTimeShort($expiryTime, $now);

                    return [
                        'text' => "🪫 ПОДПИСКА #c11e14ЗАКОНЧИТСЯ через {$remainingTime}\n\n#c11e14Нажмите сюда, чтобы продлить подписку 💳",
                        'url' => HelperService::getCurrentAppUrl() . "/access/{$uuid}/plan/select",
                    ];
                }
            }

            // Get latest announce from database
            $latestAnnounce = Announce::getLatestVisible();

            if ($latestAnnounce) {
                $url = HelperService::replaceTextVariables($latestAnnounce->url, $user);

                return [
                    'text' => $latestAnnounce->message,
                    'url' => $url ?: $defaultUrl,
                ];
            }

            // Fallback to default
            return [
                'text' => $defaultText,
                'url' => $defaultUrl,
            ];

        } catch (\Exception $e) {
            Log::error("Error getting announce data for UUID: {$uuid}", [
                'error' => $e->getMessage(),
            ]);

            return [
                'text' => $defaultText,
                'url' => $defaultUrl,
            ];
        }
    }

    /**
     * Build subscription userinfo string.
     */
    private function buildSubscriptionUserinfo(User $user): string
    {
        /** @var Subscription $subscription */
        $subscription = $user->currentSubscription;

        $subscriptionTraffic = $subscription?->trafficDetailed;
        $totalUpload = $subscriptionTraffic['up'] ?? 0;
        $totalDownload = $subscriptionTraffic['down'] ?? 0;
        $totalLimit = $subscription?->plan?->traffic_limit_bytes ?? 0;

        // If there is no traffic consumption at all, specify at least 1 byte
        // so that the traffic scale starts to be displayed in v2ray applications
        if ($totalUpload === 0 && $totalDownload === 0) {
            $totalDownload = 1;
        }

        $userinfo = sprintf('upload=%d; download=%d', $totalUpload, $totalDownload);

        // Add total only if there are actual limits set
        if ($totalLimit > 0) {
            $userinfo .= sprintf('; total=%d', $totalLimit);
        }

        // Add expire if there's an expiry time
        if ($subscription && $subscription->end_date) {
            $expireTimestamp = $subscription->end_date->timestamp;
            $userinfo .= sprintf('; expire=%d', $expireTimestamp);
        }

        return $userinfo;
    }

    /**
     * Prepare subscription data for the view.
     */
    public function prepareSubscriptionData(User $user, string $uuid): array
    {
        $subscription = $user->currentSubscription;
        $subscriptionContent = $this->generateSubscriptionContent($user);

        // Parse subscription content - for JSON format, we'll use the first config's remark
        $format = Setting::getSubscriptionFormat();
        if ($format === 'json') {
            // For JSON format, we don't have individual VLESS links
            $vlessLinks = [];
            $vlessContent = $subscriptionContent;
        } else {
            // Parse vless links from content
            $vlessLinks = array_filter(explode("\n", $subscriptionContent));
            $vlessContent = $vlessLinks[0] ?? "";
        }

        // Get statistics
        $totalUpload = $subscription?->trafficDetailed['up'] ?? 0;
        $totalDownload = $subscription?->trafficDetailed['down'] ?? 0;
        $totalLimit = $subscription?->plan?->traffic_limit_bytes ?? 0;

        // Determine status
        $earliestExpiry = $subscription?->end_date;
        $isExpired = $earliestExpiry && $earliestExpiry->isPast();
        $isLimited = $totalLimit > 0 && ($totalUpload + $totalDownload) >= $totalLimit;
        $status = $isLimited ? 'limited' : ($isExpired ? 'expired' : 'active');

        // Calculate remaining time
        $remainingTime = $this->calculateRemainingTime($earliestExpiry);
        // Get total traffic limit
        $trafficLimitBytes  = $subscription ? $subscription?->plan->traffic_limit_bytes : null;
        // Convert to GB
        $trafficLimitGb     = $subscription ? HelperService::formatTraffic($trafficLimitBytes) : null;
        // Get total traffic used
        $trafficUsedBytes = 0;
        // $trafficUsedBytes   = $subscription ? app(TrafficLoggerService::class)
        //                                                     ->getTotalTrafficForUser(
        //                                                             userId: $user->id,
        //                                                         subscriptionId: $subscription->id,
        //                                                     ) : null;

        // Convert to GB
        $trafficUsedGb              = HelperService::formatTraffic($trafficUsedBytes);
        // Convert to GB fraction
        $trafficUsedGbInFractions   = HelperService::toGbFraction($trafficUsedBytes);
        // Calculate usage percentage
        $trafficUsagePercentage     = HelperService::calculateTrafficUsagePercentage($trafficUsedBytes, $trafficLimitBytes);
        // Format remaining time
        $remainingFormatted         = HelperService::formatRemainingTimeShort($earliestExpiry, now());
        // Get user's plan
        $plan = $user?->plan;

        return [
            'uuid' => $uuid,
            'public_client_id' => $user->getPublicClientId(),
            'email' => $user->email ?? 'User',
            'subscription_url' => $user->access_url,
            'access_url' => $user->access_url,
            'vless_links' => $vlessLinks,
            'vless_content' => $vlessContent,
            'expiry_time' => $earliestExpiry,
            'remaining_time' => $remainingTime,
            'reset_interval' => 'none',
            'enable' => !$isExpired && !$isLimited,
            'status' => $status,
            'use_common_routing' => $user->use_common_routing,
            'is_sni_messenger' => $user->isSniMessenger(),
            'plan' => $plan ? [
                'name' => $plan->name,
                'duration' => $plan->duration,
                'duration_unit' => $plan->duration_unit,
                'is_demo' => $plan->is_demo,
            ] : null,
            'subscription' => $subscription ? [
                'plan_name' => $subscription->plan?->name ?? 'Unknown Plan',
                'is_active' => $subscription->isActive(),
                'is_expired' => $subscription->isExpired(),
                'started_at' => $subscription->start_date,
                'expires_at' => $subscription->end_date,
                'traffic_limit_bytes' => $trafficLimitBytes,
                'traffic_limit_gb' => $trafficLimitGb,
                'traffic_used_bytes' => $trafficUsedBytes,
                'traffic_used_gb' => $trafficUsedGb,
                'traffic_used_gb_in_fractions' => $trafficUsedGbInFractions,
                'traffic_usage_percentage' => $trafficUsagePercentage,
                'traffic_progress_color' => $trafficUsagePercentage > 90 ? 'red' : ($trafficUsagePercentage > 70 ? 'yellow' : 'green'),
                'remaining_formatted' => $remainingFormatted,
                'renewal_required' => $subscription->isRenewalRequired(),
                'is_about_to_expire' => $subscription->isAboutToExpire(),
                'is_demo' => $subscription->isDemo(),
            ] : null,
        ];
    }

    /**
     * Calculate remaining time with appropriate units.
     */
    private function calculateRemainingTime($expiryTime): array
    {
        if (!$expiryTime || $expiryTime->isPast()) {
            return [
                'value' => 0,
                'unit' => 'expired',
                'display' => 'Истекла',
                'display_en' => 'Expired'
            ];
        }

        $now = now();
        $diff = $now->diff($expiryTime);

        $totalHours = $diff->days * 24 + $diff->h;

        // If less than 1 hour, show minutes
        if ($totalHours < 1) {
            $minutes = $diff->i;
            return [
                'value' => $minutes,
                'unit' => 'minutes',
                'display' => $minutes . ' мин.',
                'display_en' => $minutes . ' min.'
            ];
        }

        // If less than 24 hours, show hours and minutes
        if ($diff->days < 1) {
            $hours = $totalHours;
            $minutes = $diff->i;

            if ($minutes > 0) {
                return [
                    'value' => $hours,
                    'unit' => 'hours',
                    'display' => $hours . ' ч. ' . $minutes . ' мин.',
                    'display_en' => $hours . ' h. ' . $minutes . ' min.'
                ];
            } else {
                return [
                    'value' => $hours,
                    'unit' => 'hours',
                    'display' => $hours . ' ч.',
                    'display_en' => $hours . ' h.'
                ];
            }
        }

        // Otherwise show days
        return [
            'value' => $diff->days,
            'unit' => 'days',
            'display' => $diff->days . ' дн.',
            'display_en' => $diff->days . ' days'
        ];
    }
}
