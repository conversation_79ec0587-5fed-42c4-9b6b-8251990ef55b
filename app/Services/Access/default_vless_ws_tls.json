{"log": {"loglevel": "warning"}, "dns": {"servers": ["*******"]}, "inbounds": [{"listen": "127.0.0.1", "port": 10808, "protocol": "mixed", "settings": {"auth": "<PERSON><PERSON><PERSON>", "udp": true, "userLevel": 8}, "sniffing": {"enabled": true, "destOverride": ["http", "tls", "quic"]}}], "outbounds": [{"tag": "proxy", "protocol": "vless", "settings": {"vnext": [{"address": "**************", "port": 8080, "users": [{"id": "0198938d-6ce8-7203-83b1-fdd90ad58233", "encryption": "none"}]}]}, "streamSettings": {"network": "ws", "security": "tls", "tlsSettings": {"serverName": "gw4.smartvpn.vip", "fingerprint": "chrome"}, "wsSettings": {"path": "/"}}}, {"tag": "direct", "protocol": "freedom"}, {"tag": "block", "protocol": "blackhole"}], "routing": {"rules": [{"type": "field", "network": "tcp,udp", "outboundTag": "proxy"}]}}