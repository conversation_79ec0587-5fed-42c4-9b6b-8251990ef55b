<?php

require_once __DIR__ . '/bootstrap/app.php';

use App\Models\User;
use App\Services\Access\AccessService;
use App\Services\HelperService;

// Initialize Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Testing Universal JSON Configuration Generation\n";
echo "==============================================\n\n";

// Create test user
$user = User::first();
if (!$user) {
    echo "✗ ERROR: No users found in database\n";
    exit(1);
}

echo "Using user: {$user->email} (ID: {$user->id})\n\n";

// Create AccessService
$accessService = new AccessService(new HelperService());

try {
    // Test JSON subscription generation
    echo "Testing JSON subscription generation...\n";
    $jsonSubscription = $accessService->generateJsonSubscription($user);
    
    if (empty($jsonSubscription) || $jsonSubscription === '[]') {
        echo "✗ WARNING: No JSON subscription generated (user may not have active subscription or servers)\n\n";
    } else {
        // Validate JSON
        $generatedConfig = json_decode($jsonSubscription, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "✗ ERROR: Generated JSON is invalid: " . json_last_error_msg() . "\n";
            echo "Generated content:\n" . $jsonSubscription . "\n";
        } else {
            echo "✓ Generated JSON is valid\n";
            echo "Number of configurations: " . count($generatedConfig) . "\n\n";
            
            // Analyze first configuration
            if (!empty($generatedConfig)) {
                $firstConfig = $generatedConfig[0];
                echo "First configuration analysis:\n";
                echo "- Has log section: " . (isset($firstConfig['log']) ? '✓' : '✗') . "\n";
                echo "- Has dns section: " . (isset($firstConfig['dns']) ? '✓' : '✗') . "\n";
                echo "- Has inbounds section: " . (isset($firstConfig['inbounds']) ? '✓' : '✗') . "\n";
                echo "- Has outbounds section: " . (isset($firstConfig['outbounds']) ? '✓' : '✗') . "\n";
                echo "- Has routing section: " . (isset($firstConfig['routing']) ? '✓' : '✗') . "\n";
                echo "- Has policy section: " . (isset($firstConfig['policy']) ? '✓' : '✗') . "\n";
                echo "- Has stats section: " . (isset($firstConfig['stats']) ? '✓' : '✗') . "\n";
                echo "- Has remarks: " . (isset($firstConfig['remarks']) ? '✓' : '✗') . "\n";
                
                if (isset($firstConfig['outbounds'])) {
                    echo "- Number of outbounds: " . count($firstConfig['outbounds']) . "\n";
                    
                    // Analyze outbounds
                    foreach ($firstConfig['outbounds'] as $index => $outbound) {
                        $tag = $outbound['tag'] ?? 'unknown';
                        $protocol = $outbound['protocol'] ?? 'unknown';
                        echo "  - Outbound {$index}: {$tag} ({$protocol})\n";
                        
                        if ($tag === 'proxy') {
                            echo "    - Has mux: " . (isset($outbound['mux']) ? '✓' : '✗') . "\n";
                            if (isset($outbound['mux'])) {
                                echo "    - Mux enabled: " . ($outbound['mux']['enabled'] ? '✓' : '✗') . "\n";
                            }
                            echo "    - Has streamSettings: " . (isset($outbound['streamSettings']) ? '✓' : '✗') . "\n";
                            if (isset($outbound['streamSettings'])) {
                                $network = $outbound['streamSettings']['network'] ?? 'unknown';
                                $security = $outbound['streamSettings']['security'] ?? 'unknown';
                                echo "    - Network: {$network}\n";
                                echo "    - Security: {$security}\n";
                                echo "    - Has sockopt: " . (isset($outbound['streamSettings']['sockopt']) ? '✓' : '✗') . "\n";
                            }
                        }
                    }
                }
                
                echo "\n";
            }
        }
    }
    
    // Test VLESS links generation for comparison
    echo "Testing VLESS links generation for comparison...\n";
    $vlessLinks = $accessService->generateVlessKeys($user);
    
    if (empty($vlessLinks)) {
        echo "✗ WARNING: No VLESS links generated\n";
    } else {
        $links = explode("\n", $vlessLinks);
        echo "✓ Generated " . count($links) . " VLESS links\n";
        echo "First link preview: " . substr($links[0], 0, 100) . "...\n";
    }
    
} catch (Exception $e) {
    echo "✗ ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\nDone!\n";
